import React, { useState, useEffect, useRef } from 'react';
import { MoreHorizontal } from 'lucide-react';

interface VulnerabilityRecord {
  id: string;
  vulnerability: string;
  target: string;
  type: string;
  severity: 'critical' | 'high' | 'medium' | 'low' | 'info';
  endpoint: string;
  attackVector: string;
  start: string;
  end: string;
  status: 'open' | 'in-progress' | 'resolved' | 'false-positive';
  alertRef?: string;
  confidence?: string;
  cweid?: string;
  desc?: string;
  pluginid?: string;
  reference?: string | string[];
  riskcode?: string;
  riskdesc?: string;
  instances?: { evidence: string }[];
  solution?: string;
  sourceid?: string;
  wascid?: string;
}

interface Props {
  vulnerabilities: VulnerabilityRecord[];
  onActionClick: (id: string, action: string) => void;
}

const VulnerabilityDataTable: React.FC<Props> = ({ vulnerabilities, onActionClick }) => {
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [expandedRow, setExpandedRow] = useState<string | null>(null);

  // Use localStorage to persist resolved/unresolved status
  const LOCAL_STORAGE_KEY = "resolvedVulnerabilities";
  const getPersistedStatus = (): Record<string, string> => {
    try {
      const data = localStorage.getItem(LOCAL_STORAGE_KEY);
      return data ? JSON.parse(data) : {};
    } catch {
      return {};
    }
  };

  const [persistedStatus, setPersistedStatus] = useState<Record<string, string>>(getPersistedStatus());

  useEffect(() => {
    setPersistedStatus(getPersistedStatus());
  }, []);

  // Merge persisted status with vulnerabilities
  const mergedVulnerabilities = vulnerabilities.map(vuln => ({
    ...vuln,
    status: persistedStatus[vuln.id] ?? vuln.status,
  }));

  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown on outside click
  useEffect(() => {
    if (!activeDropdown) return;
    const handleClick = (e: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(e.target as Node)
      ) {
        setActiveDropdown(null);
      }
    };
    document.addEventListener('mousedown', handleClick);
    return () => document.removeEventListener('mousedown', handleClick);
  }, [activeDropdown]);

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return 'text-red-800 bg-red-100 dark:text-red-400 dark:bg-red-900/20';
      case 'high':
        return 'text-orange-800 bg-orange-100 dark:text-orange-400 dark:bg-orange-900/20';
      case 'medium':
        return 'text-yellow-800 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20';
      case 'low':
        return 'text-green-800 bg-green-100 dark:text-green-400 dark:bg-green-900/20';
      case 'info':
        return 'text-blue-800 bg-blue-100 dark:text-blue-400 dark:bg-blue-900/20';
      default:
        return 'text-neutral-800 bg-neutral-100 dark:text-neutral-400 dark:bg-neutral-900/20';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'open':
        return 'text-red-400 bg-red-900/20';
      case 'in-progress':
        return 'text-yellow-400 bg-yellow-900/20';
      case 'resolved':
        return 'text-green-600 bg-green-500/20';
      case 'false-positive':
        return 'text-neutral-400 bg-neutral-900/20';
      default:
        return 'text-neutral-400 bg-neutral-900/20';
    }
  };

  const formatStatus = (status: string) => {
    switch (status.toLowerCase()) {
      case 'in-progress':
        return 'Processing';
      case 'false-positive':
        return 'False Positive';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  const handleMarkResolved = (id: string) => {
    const updated = { ...persistedStatus, [id]: 'resolved' };
    setPersistedStatus(updated);
    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(updated));
    setActiveDropdown(null);
    onActionClick(id, 'resolve');
  };

  const handleMarkUnResolved = (id: string) => {
    const updated = { ...persistedStatus, [id]: 'open' };
    setPersistedStatus(updated);
    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(updated));
    setActiveDropdown(null);
    onActionClick(id, 'unresolve');
  };

  const ActionDropdown = ({ vulnerabilityId }: { vulnerabilityId: string }) => {
    const isOpen = activeDropdown === vulnerabilityId;
    const vuln = mergedVulnerabilities.find(v => v.id === vulnerabilityId);

    return (
      <div className="relative" ref={isOpen ? dropdownRef : undefined}>
        <button
          onClick={() => setActiveDropdown(isOpen ? null : vulnerabilityId)}
          className="p-1 rounded hover:bg-neutral-700 transition-colors"
        >
          <MoreHorizontal className="w-4 h-4 text-neutral-400" />
        </button>

        {isOpen && (
          <div className="absolute right-0 top-8 bg-white dark:bg-[#171717] border-neutral-200 rounded-md shadow-lg z-10 min-w-[120px] action-dropdown-menu">
            <button
              onClick={() => {
                onActionClick(vulnerabilityId, 'view');
                setActiveDropdown(null);
                setExpandedRow(vulnerabilityId);
              }}
              className="w-full text-left px-3 py-2 text-sm text-neutral-700 dark:text-neutral-200 hover:bg-neutral-50 dark:hover:bg-neutral-800 transition-colors"
            >
              View Details
            </button>
            {vuln?.status === 'resolved' ? (
              <button
                onClick={() => handleMarkUnResolved(vulnerabilityId)}
                className="w-full text-left px-3 py-2 text-sm text-neutral-700 dark:text-neutral-200 hover:bg-neutral-50 dark:hover:bg-neutral-800 transition-colors"
              >
                Mark Un-Resolved
              </button>
            ) : (
              <button
                onClick={() => handleMarkResolved(vulnerabilityId)}
                className="w-full text-left px-3 py-2 text-sm text-neutral-700 dark:text-neutral-200 hover:bg-neutral-50 dark:hover:bg-neutral-800 transition-colors"
              >
                Mark Resolved
              </button>
            )}
            <button
              onClick={() => {
                onActionClick(vulnerabilityId, 'false-positive');
                setActiveDropdown(null);
              }}
              className="w-full text-left px-3 py-2 text-sm text-neutral-700 dark:text-neutral-200 hover:bg-neutral-50 dark:hover:bg-neutral-800 transition-colors"
            >
              False Positive
            </button>
            <button
              onClick={() => {
                onActionClick(vulnerabilityId, 'export');
                setActiveDropdown(null);
              }}
              className="w-full text-left px-3 py-2 text-sm text-neutral-700 dark:text-neutral-200 hover:bg-neutral-50 dark:hover:bg-neutral-800 transition-colors border-t dark:border-neutral-600 border-neutral-200"
            >
              Export
            </button>
          </div>
        )}
      </div>
    );
  };

  // Helper to strip <p> tags from a string
  const stripPTags = (str?: string) =>
    typeof str === "string"
      ? str.replace(/<\/?p>/gi, "")
      : str;

  // Helper to split by <p> tags and strip them
  const splitAndStripPTags = (str?: string) =>
    typeof str === "string"
      ? str
          .split(/<\/?p>/gi)
          .map(s => s.trim())
          .filter(Boolean)
      : [];

  return (
    <div className="bg-[#2A2A2A] dark:bg-[#2A2A2A] bg-white rounded-lg overflow-hidden border border-neutral-200 dark:border-neutral-700">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-[#1A1A1A] dark:bg-[#1A1A1A] bg-neutral-50">
            <tr>
              <th className="text-left py-3 px-4 text-sm font-medium text-neutral-600 dark:text-neutral-300 tracking-wide">
                Sr.No
              </th>
              <th className="text-left py-3 px-4 text-sm font-medium text-neutral-600 dark:text-neutral-300 tracking-wide">
                Vulnerability
              </th>
              <th className="text-left py-3 px-4 text-sm font-medium text-neutral-600 dark:text-neutral-300 tracking-wide">
                Target
              </th>
              <th className="text-left py-3 px-4 text-sm font-medium text-neutral-600 dark:text-neutral-300 tracking-wide">
                Type
              </th>
              <th className="text-left py-3 px-4 text-sm font-medium text-neutral-600 dark:text-neutral-300 tracking-wide">
                Severity
              </th>
              <th className="text-left py-3 px-4 text-sm font-medium text-neutral-600 dark:text-neutral-300 tracking-wide">
                Endpoint
              </th>
              <th className="text-left py-3 px-4 text-sm font-medium text-neutral-600 dark:text-neutral-300 tracking-wide">
                Status
              </th>
              <th className="text-left py-3 px-4 text-sm font-medium text-neutral-600 dark:text-neutral-300 tracking-wide">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-neutral-200 dark:divide-neutral-700">
            {mergedVulnerabilities.map((vuln, idx) => (
              <React.Fragment key={vuln.id}>
                <tr
                  className={`hover:bg-neutral-50 dark:hover:bg-neutral-800/50 transition-colors cursor-pointer ${expandedRow === vuln.id ? 'bg-neutral-50 dark:bg-neutral-800/50' : ''}`}
                  onClick={(e) => {
                    // Only expand row if NOT clicking inside the Actions cell
                    // Prevent row expansion when clicking the ActionDropdown button
                    if (
                      (e.target as HTMLElement).closest('.action-dropdown-btn') ||
                      (e.target as HTMLElement).closest('.action-dropdown-menu')
                    ) {
                      return;
                    }
                    setExpandedRow(expandedRow === vuln.id ? null : vuln.id);
                  }}
                >
                  <td className="py-3 px-4 text-sm text-neutral-800 dark:text-neutral-200">
                    {idx + 1}
                  </td>
                  <td className="py-3 px-4 text-sm text-neutral-800 dark:text-neutral-200">
                    <div className="max-w-[200px] truncate" title={vuln.vulnerability}>
                      {vuln.vulnerability}
                    </div>
                  </td>
                  <td className="py-3 px-4 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 cursor-pointer">
                    <div className="max-w-[150px] truncate" title={vuln.target}>
                      {vuln.target}
                    </div>
                  </td>
                  <td className="py-3 px-4 text-sm text-neutral-800 dark:text-neutral-200">
                    <div className="max-w-[120px] truncate" title={vuln.type}>
                      {vuln.type}
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getSeverityColor(vuln.severity)}`}>
                      {vuln.severity.toUpperCase()}
                    </span>
                  </td>
                  <td className="py-3 px-4 text-sm text-neutral-800 dark:text-neutral-200">
                    <div className="max-w-[150px] truncate" title={vuln.endpoint}>
                      {vuln.endpoint}
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(vuln.status)}`}>
                      {formatStatus(vuln.status)}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    {/* Add a class to the ActionDropdown button for event filtering */}
                    <span className="action-dropdown-btn">
                      <ActionDropdown vulnerabilityId={vuln.id} />
                    </span>
                  </td>
                </tr>
                {expandedRow === vuln.id && (
                  <tr>
                    <td colSpan={10} >
                      <div className="bg-neutral-50 dark:bg-[#181818] py-3 px-4 ">
                        <div className="flex flex-col gap-4 md:grid md:grid-cols-12 md:gap-6">
                          {/* Description & Solution */}
                          <div className="md:col-span-6 flex flex-col gap-4">
                            <div className="rounded-xl bg-white dark:bg-neutral-900/80 border border-neutral-200 dark:border-neutral-800 p-4 shadow-sm">
                              <h3 className="font-semibold text-base text-neutral-800 dark:text-neutral-100 mb-2">Description</h3>
                              <div
                                className="text-sm text-neutral-600 dark:text-neutral-300 max-h-40 overflow-auto rounded scrollbar-thin scrollbar-thumb-neutral-300 dark:scrollbar-thumb-neutral-700"
                                style={{ wordBreak: 'break-word' }}
                                tabIndex={0}
                              >
                                {stripPTags(vuln.desc) || 'Not available'}
                              </div>
                            </div>
                            <div className="rounded-xl bg-white dark:bg-neutral-900/80 border border-neutral-200 dark:border-neutral-800 p-4 shadow-sm">
                              <h3 className="font-semibold text-base text-neutral-800 dark:text-neutral-100 mb-2">Solution</h3>
                              <div
                                className="text-sm text-neutral-600 dark:text-neutral-300 max-h-40 overflow-auto rounded scrollbar-thin scrollbar-thumb-neutral-300 dark:scrollbar-thumb-neutral-700"
                                style={{ wordBreak: 'break-word' }}
                                tabIndex={0}
                              >
                                {stripPTags(vuln.solution) || 'No solution provided'}
                              </div>
                            </div>
                            {/* Evidence moved here, same width */}
                            <div className="rounded-xl bg-white dark:bg-neutral-900/80 border border-neutral-200 dark:border-neutral-800 p-4 shadow-sm">
                              <h3 className="font-semibold text-base text-neutral-800 dark:text-neutral-100 mb-2">Evidence</h3>
                              <div
                                className="max-h-32 overflow-auto text-sm text-neutral-600 dark:text-neutral-300 rounded scrollbar-thin scrollbar-thumb-neutral-300 dark:scrollbar-thumb-neutral-700"
                                tabIndex={0}
                                style={{ wordBreak: 'break-word' }}
                              >
                                {vuln.instances && Array.isArray(vuln.instances) && vuln.instances.length > 0 ? (
                                  <ul className="list-disc list-inside space-y-1">
                                    {vuln.instances.map((inst, i) =>
                                      inst.evidence
                                        ? <li key={i} className="break-all">{stripPTags(inst.evidence)}</li>
                                        : null
                                    )}
                                  </ul>
                                ) : (
                                  <p className="text-sm text-neutral-500 dark:text-neutral-400">No evidence found</p>
                                )}
                              </div>
                            </div>
                          </div>
                          {/* Details */}
                          <div className="md:col-span-3 flex flex-col gap-4">
                            <div className="rounded-xl bg-white dark:bg-neutral-900/80 border border-neutral-200 dark:border-neutral-800 p-4 shadow-sm">
                              <h3 className="font-semibold text-base text-neutral-800 dark:text-neutral-100 mb-3">Details</h3>
                              <div className="space-y-2 text-sm">
                                <div className="flex justify-between gap-2">
                                  <span className="text-neutral-500 dark:text-neutral-400">Confidence:</span>
                                  <span className="font-medium text-neutral-800 dark:text-neutral-200">{vuln.confidence || 'N/A'}</span>
                                </div>
                                <div className="flex justify-between gap-2">
                                  <span className="text-neutral-500 dark:text-neutral-400">CWE ID:</span>
                                  <span className="font-medium text-neutral-800 dark:text-neutral-200">{vuln.cweid || 'N/A'}</span>
                                </div>
                                <div className="flex justify-between gap-2">
                                  <span className="text-neutral-500 dark:text-neutral-400">Plugin ID:</span>
                                  <span className="font-medium text-neutral-800 dark:text-neutral-200">{vuln.pluginid || 'N/A'}</span>
                                </div>
                                <div className="flex justify-between gap-2">
                                  <span className="text-neutral-500 dark:text-neutral-400">Alert Ref:</span>
                                  <span className="font-medium text-neutral-800 dark:text-neutral-200">{vuln.alertRef || 'N/A'}</span>
                                </div>
                                <div className="flex justify-between gap-2">
                                  <span className="text-neutral-500 dark:text-neutral-400">Risk Code:</span>
                                  <span className="font-medium text-neutral-800 dark:text-neutral-200">{vuln.riskcode || 'N/A'}</span>
                                </div>
                                <div className="flex justify-between gap-2">
                                  <span className="text-neutral-500 dark:text-neutral-400">Risk Desc:</span>
                                  <span className="font-medium text-neutral-800 dark:text-neutral-200">{vuln.riskdesc || 'N/A'}</span>
                                </div>
                                <div className="flex justify-between gap-2">
                                  <span className="text-neutral-500 dark:text-neutral-400">Source ID:</span>
                                  <span className="font-medium text-neutral-800 dark:text-neutral-200">{vuln.sourceid || 'N/A'}</span>
                                </div>
                                <div className="flex justify-between gap-2">
                                  <span className="text-neutral-500 dark:text-neutral-400">WASC ID:</span>
                                  <span className="font-medium text-neutral-800 dark:text-neutral-200">{vuln.wascid || 'N/A'}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                          {/* References */}
                          <div className="md:col-span-3 flex flex-col gap-4">
                            <div
                              className="rounded-xl bg-white dark:bg-neutral-900/80 border border-neutral-200 dark:border-neutral-800 p-4 shadow-sm"
                              style={{ maxHeight: 330, overflowY: 'auto', height: 'fit-content' }}
                            >
                              <h3 className="font-semibold text-base text-neutral-800 dark:text-neutral-100 mb-2">References</h3>
                              <div
                                className="text-sm text-blue-600 dark:text-blue-400 break-all overflow-auto rounded scrollbar-thin scrollbar-thumb-neutral-300 dark:scrollbar-thumb-neutral-700"
                                tabIndex={0}
                                style={{ wordBreak: 'break-word' }}
                              >
                                {vuln.reference ? (
                                  Array.isArray(vuln.reference) ? (
                                    vuln.reference.map((ref, i) =>
                                      splitAndStripPTags(ref).map((link, j) => (
                                        <a key={i + '-' + j} href={link} target="_blank" rel="noopener noreferrer" className="hover:underline block mb-1">{link}</a>
                                      ))
                                    )
                                  ) : (
                                    splitAndStripPTags(vuln.reference).map((link, i) => (
                                      <a key={i} href={link} target="_blank" rel="noopener noreferrer" className="hover:underline block mb-1">{link}</a>
                                    ))
                                  )
                                ) : (
                                  <p className="text-sm text-neutral-500 dark:text-neutral-400">No references available</p>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
      
      {vulnerabilities.length === 0 && (
        <div className="text-center py-12">
          <p className="text-neutral-600 dark:text-neutral-400">No vulnerabilities found matching the current filters.</p>
        </div>
      )}
    </div>
  );
};

export default VulnerabilityDataTable;