/**
 * Environment configuration utility
 * Maps environment variables to ensure compatibility
 */

// Helper function to get environment variables with fallbacks
const getEnvVar = (viteKey: string, reactKey: string): string | undefined => {
  return import.meta.env[viteKey] || import.meta.env[reactKey];
};

export const ENV_CONFIG = {
  // API Keys
  OPENAI_API_KEY: getEnvVar('VITE_OPENAI_API_KEY', 'REACT_APP_OPENAI_API_KEY'),
  NEWS_API_KEY: getEnvVar('VITE_NEWS_API_KEY', 'REACT_APP_NEWS_API_KEY'),
  GNEWS_API_KEY: getEnvVar('VITE_GNEWS_API_KEY', 'REACT_APP_GNEWS_API_KEY'),
  NEWSDATA_API_KEY: getEnvVar('VITE_NEWSDATA_API_KEY', 'REACT_APP_NEWSDATA_API_KEY'),

  // EmailJS Configuration
  EMAILJS_SERVICE_ID: getEnvVar('VITE_EMAILJS_SERVICE_ID', 'REACT_APP_EMAILJS_SERVICE_ID'),
  EMAILJS_TEMPLATE_ID: getEnvVar('VITE_EMAILJS_TEMPLATE_ID', 'REACT_APP_EMAILJS_TEMPLATE_ID'),
  EMAILJS_PUBLIC_KEY: getEnvVar('VITE_EMAILJS_PUBLIC_KEY', 'REACT_APP_EMAILJS_PUBLIC_KEY'),

  // Web3Forms Configuration (Alternative email service)
  WEB3FORMS_ACCESS_KEY: getEnvVar('VITE_WEB3FORMS_ACCESS_KEY', 'REACT_APP_WEB3FORMS_ACCESS_KEY'),

  // API URLs
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'https://aipt-be-server.onrender.com',
} as const;

export default ENV_CONFIG;
