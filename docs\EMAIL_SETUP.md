# Email Service Setup Guide

This guide explains how to set up real email sending for the Defendly dashboard assessment requests.

## 🚀 Quick Setup Options

### Option 1: EmailJS (Recommended - Free & Easy)

EmailJS allows you to send emails directly from the frontend without a backend server.

#### Step 1: Create EmailJS Account
1. Go to [EmailJS.com](https://www.emailjs.com/)
2. Sign up for a free account (1000 emails/month free)
3. Verify your email address

#### Step 2: Set up Email Service
1. In EmailJS dashboard, go to **Email Services**
2. Click **Add New Service**
3. Choose your email provider (Gmail, Outlook, etc.)
4. Follow the setup instructions for your provider
5. Note down your **Service ID** (e.g., `service_gmail123`)

#### Step 3: Create Email Template
1. Go to **Email Templates** in EmailJS dashboard
2. Click **Create New Template**
3. Use this template content:

```html
Subject: New Security Assessment Request - {{assessmentType}}

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Security Assessment Request</title>
</head>
<body>
    <h2>🛡️ New Security Assessment Request</h2>
    
    <p><strong>Organization:</strong> {{organizationName}}</p>
    <p><strong>Project:</strong> {{projectName}}</p>
    <p><strong>Assessment Type:</strong> {{assessmentType}}</p>
    <p><strong>Description:</strong> {{assessmentDescription}}</p>
    
    <h3>Assessment Details:</h3>
    {{message_html}}
    
    <p><strong>Submitted at:</strong> {{submittedAt}}</p>
    
    <hr>
    <p><em>This email was sent from Defendly Security Assessment Platform.</em></p>
</body>
</html>
```

4. Set template variables:
   - `to_email` → `<EMAIL>`
   - `from_email` → Your sender email
   - `subject` → Email subject
   - `message_html` → Assessment details
   - `organizationName`, `projectName`, `assessmentType`, etc.

5. Save and note down your **Template ID** (e.g., `template_abc123`)

#### Step 4: Get Public Key
1. Go to **Account** → **General**
2. Copy your **Public Key** (e.g., `user_abc123xyz`)

#### Step 5: Update Environment Variables
Add these to your `.env` file:

```env
VITE_EMAILJS_SERVICE_ID=your_service_id_here
VITE_EMAILJS_TEMPLATE_ID=your_template_id_here
VITE_EMAILJS_PUBLIC_KEY=your_public_key_here
```

### Option 2: Formspree (Alternative - Simple Form Backend)

Formspree provides a simple form backend that can send emails.

#### Setup:
1. Go to [Formspree.io](https://formspree.io/)
2. Sign up for free account
3. Create a new form with endpoint like `https://formspree.io/f/YOUR_FORM_ID`
4. Update the email service to use Formspree endpoint

### Option 3: Web3Forms (Free Alternative)

Web3Forms is a free form backend service.

#### Setup:
1. Go to [Web3Forms.com](https://web3forms.com/)
2. Get your free access key
3. No signup required for basic usage

## 🔧 Implementation Status

The current implementation includes:

✅ **EmailJS Integration** - Ready to use once configured
✅ **Environment Configuration** - Supports multiple email services
✅ **Fallback System** - Falls back to mock emails if not configured
✅ **Professional Templates** - HTML email templates with styling
✅ **Error Handling** - Graceful error handling and logging

## 🧪 Testing

### Test with Real Emails
1. Configure EmailJS as described above
2. Submit an assessment request
3. Check your email inbox for the assessment details

### Test with Mock Emails (Development)
If email services aren't configured, the system will log mock emails to the console for testing.

## 🛠️ Troubleshooting

### Common Issues:

1. **"EmailJS not configured" error**
   - Make sure all environment variables are set correctly
   - Restart the development server after adding .env variables

2. **Emails not being received**
   - Check spam/junk folder
   - Verify EmailJS service is active
   - Check EmailJS dashboard for delivery logs

3. **Template errors**
   - Ensure all template variables match the ones being sent
   - Test template in EmailJS dashboard first

### Debug Steps:
1. Check browser console for error messages
2. Verify environment variables are loaded: `console.log(import.meta.env)`
3. Test EmailJS configuration in their dashboard
4. Check console logs for mock email output during development

## 📧 Email Content

The system sends detailed assessment requests including:
- Organization and project information
- Selected assessment type and description
- All form details specific to the assessment type
- Timestamp and formatting
- Professional HTML styling

## 🔒 Security Notes

- EmailJS public key is safe to expose in frontend code
- Never expose private API keys in frontend code
- Consider rate limiting for production use
- EmailJS free tier has monthly limits (1000 emails/month)

## 📞 Support

If you need help setting up email services:
1. Check EmailJS documentation: https://www.emailjs.com/docs/
2. Review browser console for error messages
3. Test with mock email system first
4. Verify all configuration steps are completed
