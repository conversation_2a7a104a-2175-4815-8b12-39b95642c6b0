import React from 'react';
import { Routes, Route, useNavigate } from 'react-router-dom';
import Navbar from './components/layout/Navbar';
import Sidebar from './components/layout/Sidebar';
import Dashboard from './pages/Dashboard';
import RecentReports from './components/reports/RecentReports';
import Settings from './components/settings/Settings';
import NotFound from './pages/NotFound';
import SearchResults from './pages/SearchResults';
import AllVulnerabilities from './pages/AllVulnerabilities';
import AllPorts from './pages/AllPorts';
import AssetsInventory from './pages/AssetsInventory';
import AllSecurityHeaders from './pages/AllSecurityHeaders';
import AssessmentCenter from './pages/AssessmentCenter';
import AdvancedTesting from './pages/AdvancedTesting';
import VulnerabilityHub from './pages/VulnerabilityHub';
import ScanReview from './pages/ScanReview';
import Login from './pages/Login';
import Profile from './pages/Profile';
import { AuthProvider } from './context/AuthContext';
import ProtectedRoute from './components/auth/ProtectedRoute';
import { ThemeProvider } from './context/ThemeContext'; // <-- Add this import
import ErrorBoundary from './components/common/ErrorBoundary';


function App() {
  const navigate = useNavigate();
  
  const handleSearch = (query: string) => {
    navigate(`/search?q=${encodeURIComponent(query)}`);
  };
  
  return (
    <ErrorBoundary>
      <ThemeProvider> {/* <-- Wrap the app with ThemeProvider */}
        <AuthProvider>
          <Routes>
          <Route path="/login" element={<Login />} />
          
          {/* Protected Routes */}
          <Route
            path="/"
            element={
              <ProtectedRoute>
                <div className="min-h-screen bg-[#F1F8FD] dark:bg-[#161717] flex flex-col">
                  <Navbar onSearch={handleSearch} />
                  <Sidebar />
                  <main className="flex-1 pl-0 lg:pl-16 pt-16">
                    <Dashboard />
                  </main>
                </div>
              </ProtectedRoute>
            }
          >
            <Route index element={<AllVulnerabilities />} />
            <Route path="vulnerabilities" element={<AllVulnerabilities />} />
            <Route path="ports" element={<AllPorts />} />
            <Route path="assets" element={<AssetsInventory />} />
            <Route path="endpoints" element={<AssetsInventory />} /> {/* Optional: keep this for backward compatibility */}
            <Route path="security-headers" element={<AllSecurityHeaders />} />
          </Route>
          
          <Route
            path="/assessment"
            element={
              <ProtectedRoute>
                <div className="min-h-screen bg-[#F1F8FD] dark:bg-[#161717] flex flex-col">
                  <Navbar onSearch={handleSearch} />
                  <Sidebar />
                  <main className="flex-1 pl-0 lg:pl-16 pt-16">
                    <AssessmentCenter />
                  </main>
                </div>
              </ProtectedRoute>
            }
          />

          <Route
            path="/assessment/review"
            element={
              <ProtectedRoute>
                <div className="min-h-screen bg-[#F1F8FD] dark:bg-[#161717] flex flex-col">
                  <Navbar onSearch={handleSearch} />
                  <Sidebar />
                  <main className="flex-1 pl-0 lg:pl-16 pt-16">
                    <ScanReview />
                  </main>
                </div>
              </ProtectedRoute>
            }
          />

          {/* Additional protected routes */}
          <Route
            path="/assessment/advanced"
            element={
              <ProtectedRoute>
                <div className="min-h-screen bg-[#F1F8FD] dark:bg-[#161717] flex flex-col">
                  <Navbar onSearch={handleSearch} />
                  <Sidebar />
                  <main className="flex-1 pl-0 lg:pl-16 pt-16">
                    <AdvancedTesting />
                  </main>
                </div>
              </ProtectedRoute>
            }
          />

          <Route
            path="/vulnerability-hub"
            element={
              <ProtectedRoute>
                <div className="min-h-screen bg-[#F1F8FD] dark:bg-[#161717] flex flex-col">
                  <Navbar onSearch={handleSearch} />
                  <Sidebar />
                  <main className="flex-1 pl-0 lg:pl-16 pt-16">
                    <VulnerabilityHub />
                  </main>
                </div>
              </ProtectedRoute>
            }
          />

          <Route
            path="/reports"
            element={
              <ProtectedRoute>
                <div className="min-h-screen bg-[#F1F8FD] dark:bg-[#161717] flex flex-col">
                  <Navbar onSearch={handleSearch} />
                  <Sidebar />
                  <main className="flex-1 pl-0 lg:pl-16 pt-16">
                    <RecentReports />
                  </main>
                </div>
              </ProtectedRoute>
            }
          />

          <Route
            path="/settings"
            element={
              <ProtectedRoute>
                <div className="min-h-screen bg-[#F1F8FD] dark:bg-[#161717] flex flex-col">
                  <Navbar onSearch={handleSearch} />
                  <Sidebar />
                  <main className="flex-1 pl-0 lg:pl-16 pt-16">
                    <Settings />
                  </main>
                </div>
              </ProtectedRoute>
            }
          />

          <Route
            path="/profile"
            element={
              <ProtectedRoute>
                <div className="min-h-screen bg-[#F1F8FD] dark:bg-[#161717] flex flex-col">
                  <Navbar onSearch={handleSearch} />
                  <Sidebar />
                  <main className="flex-1 pl-0 lg:pl-16 pt-16">
                    <Profile />
                  </main>
                </div>
              </ProtectedRoute>
            }
          />

          <Route
            path="/search"
            element={
              <ProtectedRoute>
                <div className="min-h-screen bg-[#F1F8FD] dark:bg-[#161717] flex flex-col">
                  <Navbar onSearch={handleSearch} />
                  <Sidebar />
                  <main className="flex-1 pl-0 lg:pl-16 pt-16">
                    <SearchResults />
                  </main>
                </div>
              </ProtectedRoute>
            }
          />

          <Route path="*" element={<NotFound />} />
          </Routes>
        </AuthProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;