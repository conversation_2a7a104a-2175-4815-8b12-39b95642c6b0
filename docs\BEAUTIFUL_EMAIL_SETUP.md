# 🎨 Beautiful HTML Email Setup Guide

Get stunning, professional HTML emails with CSS gradients and modern styling!

## 🌟 What You'll Get

With EmailJS configured, your assessment emails will have:
- ✨ **Beautiful CSS gradients** and modern styling
- 🎨 **Professional layout** with cards and sections
- 📱 **Mobile responsive** design
- 🛡️ **Branded colors** matching Defendly theme
- 🔍 **Interactive elements** with hover effects
- 📧 **Rich HTML formatting** instead of plain text

## 🚀 Quick EmailJS Setup (5 minutes)

### Step 1: Create EmailJS Account
1. Go to [EmailJS.com](https://www.emailjs.com/)
2. Click "Sign Up" and create a free account
3. Verify your email address

### Step 2: Add Email Service
1. In EmailJS dashboard, go to **"Email Services"**
2. Click **"Add New Service"**
3. Choose your email provider:
   - **Gmail** (recommended)
   - **Outlook/Hotmail**
   - **Yahoo**
   - Or any other SMTP service
4. Follow the connection steps for your provider
5. **Copy the Service ID** (e.g., `service_gmail123`)

### Step 3: Create Email Template
1. Go to **"Email Templates"**
2. Click **"Create New Template"**
3. Set template name: `Assessment Request`
4. **Important**: Use this exact template content:

#### Template Settings:
- **To Email**: `{{to_email}}`
- **From Name**: `Defendly Security Platform`
- **From Email**: Your connected email
- **Subject**: `{{subject}}`

#### Template Content (HTML):
```html
{{{message_html}}}
```

**Important**: Use triple braces `{{{message_html}}}` to allow HTML content!

5. **Save and copy the Template ID** (e.g., `template_abc123`)

### Step 4: Get Public Key
1. Go to **"Account"** → **"General"**
2. Copy your **Public Key** (e.g., `user_abc123xyz`)

### Step 5: Update Environment Variables
Add these to your `.env` file:

```env
# Existing variables
VITE_OPENAI_API_KEY=your_openai_key
VITE_API_BASE_URL=http://localhost:5000
VITE_WEB3FORMS_ACCESS_KEY=your_web3forms_key

# Add these EmailJS variables
VITE_EMAILJS_SERVICE_ID=your_service_id_here
VITE_EMAILJS_TEMPLATE_ID=your_template_id_here
VITE_EMAILJS_PUBLIC_KEY=your_public_key_here
```

### Step 6: Restart and Test
```bash
# Stop the server (Ctrl+C)
npm run dev
```

## 🧪 Testing

### Check Console Output:
When you submit an assessment, you should see:
- 🚀 `Starting email send process...`
- 📧 `Attempting to send via EmailJS (HTML email)...`
- ✅ `Email sent successfully via EmailJS`

### If EmailJS is not configured:
- ⚠️ `EmailJS not configured - skipping HTML email option`
- 📧 `Attempting to send via Web3Forms (plain text)...`

## 🎨 Email Preview

With EmailJS configured, your emails will look like this:

```
┌─────────────────────────────────────────┐
│  🛡️ Security Assessment Request         │  ← Beautiful gradient header
│  New request received from your platform │
├─────────────────────────────────────────┤
│                                         │
│  ┌─────────────────┐ ┌─────────────────┐ │
│  │ ORGANIZATION    │ │ PROJECT         │ │  ← Info cards with gradients
│  │ Bug Hunters     │ │ Testing Project │ │
│  └─────────────────┘ └─────────────────┘ │
│                                         │
│  🔍 Assessment Details                   │  ← Styled details section
│  ┌─────────────────────────────────────┐ │
│  │ WEB APPLICATION URL                 │ │
│  │ https://app.defendly.ai             │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  📅 Submitted: 7/13/2025, 3:55:40 PM   │  ← Styled timestamp
│                                         │
│  This email was automatically generated │
│  by Defendly Security Platform          │
└─────────────────────────────────────────┘
```

## 🔄 Current vs New Email Comparison

### Current (Web3Forms - Plain Text):
```
🛡️ NEW SECURITY ASSESSMENT REQUEST

Organization: Bug Hunters
Project: Testing Project
Assessment Type: Web Application
...
```

### New (EmailJS - Beautiful HTML):
- 🎨 Gradient backgrounds and modern styling
- 📱 Mobile responsive layout
- 🃏 Card-based information display
- 🎯 Interactive hover effects
- 🌈 Brand colors and professional typography

## 🛠️ Troubleshooting

### "EmailJS not configured" message:
- Verify all three environment variables are set
- Restart the development server
- Check that values don't contain "YOUR_" prefix

### Emails not received:
- Check spam/junk folder
- Verify EmailJS service is connected
- Test template in EmailJS dashboard first

### Template errors:
- Make sure to use `{{{message_html}}}` with triple braces
- Set "To Email" field to `{{to_email}}`
- Publish the template after creating it

## 💡 Pro Tips

1. **Test First**: Test your EmailJS template in their dashboard before using it
2. **Mobile Preview**: EmailJS shows mobile preview - make sure it looks good
3. **Spam Prevention**: Use a verified domain for better deliverability
4. **Rate Limits**: Free tier has 200 emails/month - upgrade if needed

## 🎯 Next Steps

1. Set up EmailJS following the steps above
2. Test an assessment submission
3. Check your email for the beautiful HTML version
4. Enjoy professional-looking assessment notifications!

The system will automatically use EmailJS for beautiful HTML emails when configured, and fall back to Web3Forms plain text if not configured.
