# 🚀 Quick Email Setup (5 Minutes)

Get real email sending working in 5 minutes using Web3Forms (completely free, no signup required).

## Option 1: Web3Forms (Easiest - No Signup Required)

### Step 1: Get Access Key
1. Go to https://web3forms.com/
2. Scroll down to "Get Your Access Key"
3. Enter your email: `<EMAIL>`
4. Click "Get Access Key"
5. Copy the access key (looks like: `abc123-def456-ghi789`)

### Step 2: Add to Environment
Add this line to your `.env` file:
```env
VITE_WEB3FORMS_ACCESS_KEY=your_access_key_here
```

### Step 3: Restart Development Server
```bash
# Stop the current server (Ctrl+C)
# Then restart:
npm run dev
```

### Step 4: Test
1. Go to the Advanced Testing page
2. Fill out the assessment form
3. Submit - you should receive a real email!

---

## Option 2: EmailJS (More Features)

### Step 1: Create Account
1. Go to https://www.emailjs.com/
2. Sign up with your email
3. Verify your account

### Step 2: Add Email Service
1. Go to "Email Services" → "Add New Service"
2. Choose Gmail/Outlook/Yahoo
3. Connect your email account
4. Copy the Service ID

### Step 3: Create Template
1. Go to "Email Templates" → "Create New Template"
2. Set template name: "Assessment Request"
3. Use this template:

**Subject:** `New Assessment Request - {{assessmentType}}`

**Content:**
```html
<h2>New Security Assessment Request</h2>
<p><strong>Organization:</strong> {{organizationName}}</p>
<p><strong>Project:</strong> {{projectName}}</p>
<p><strong>Assessment Type:</strong> {{assessmentType}}</p>
<p><strong>Details:</strong></p>
{{{message_html}}}
<p><strong>Submitted:</strong> {{submittedAt}}</p>
```

4. Set "To Email" to: `<EMAIL>`
5. Save and copy Template ID

### Step 4: Get Public Key
1. Go to "Account" → "General"
2. Copy your Public Key

### Step 5: Add to Environment
Add these to your `.env` file:
```env
VITE_EMAILJS_SERVICE_ID=your_service_id
VITE_EMAILJS_TEMPLATE_ID=your_template_id
VITE_EMAILJS_PUBLIC_KEY=your_public_key
```

### Step 6: Restart and Test
```bash
npm run dev
```

---

## 🔧 Current .env File Should Look Like:

```env
VITE_OPENAI_API_KEY=********************************************************************************************************************************************************************
VITE_API_BASE_URL=http://localhost:5000

# Add ONE of these email configurations:

# Option 1: Web3Forms (Easiest)
VITE_WEB3FORMS_ACCESS_KEY=your_web3forms_access_key

# Option 2: EmailJS (More features)
VITE_EMAILJS_SERVICE_ID=your_service_id
VITE_EMAILJS_TEMPLATE_ID=your_template_id
VITE_EMAILJS_PUBLIC_KEY=your_public_key
```

---

## 🧪 Testing

### Check if it's working:
1. Open browser console (F12)
2. Submit an assessment request
3. Look for these messages:
   - ✅ `Email sent successfully via Web3Forms` or
   - ✅ `Email sent successfully via EmailJS`
   - ❌ If you see "Mock Email Sent" - configuration is not working

### Debug:
```javascript
// In browser console:
console.log(import.meta.env) // Check if your env variables are loaded
// Check console logs for mock email output if real emails aren't working
```

---

## 📧 What You'll Receive

You'll get a professional email with:
- Organization and project details
- Assessment type and description
- All form fields filled out
- Timestamp
- Professional HTML formatting

---

## ⚡ Troubleshooting

**Problem:** Still getting mock emails
- **Solution:** Make sure you added the env variable correctly and restarted the server

**Problem:** Web3Forms not working
- **Solution:** Check the access key is correct and your email is valid

**Problem:** EmailJS not working
- **Solution:** Verify all three IDs are correct and template is published

**Problem:** No emails received
- **Solution:** Check spam folder, verify email address is correct

---

## 🎯 Recommendation

**Start with Web3Forms** - it's the easiest and works immediately without any account setup. You can always upgrade to EmailJS later for more features.
